from typing import List, Dict, Union
import json
import numpy as np
from .embedder import <PERSON><PERSON><PERSON><PERSON><PERSON>, EmbeddingGenerator, LanceDBManager, VectorIndexer
from .retrievers import MultiVectorRetriever

db_path = "./lancedb"
db_manager = LanceDBManager(db_path=db_path)
table_name = "text_pages_v3"

def get_dense_retriever():
    if dense_retriever is None:
        try:
            db_manager = db_manager
            text_embedder = "Qwen/Qwen3-Embedding-0.6B"
            dense_retriever = MultiVectorRetriever(
                db_manager,
                text_embedder
            )
        except Exception as e:
            print(f"Failed to initialise dense retriever: {e}")
            dense_retriever = None

    return dense_retriever

def query(questions: List[str]) -> List[str]:
    dense_retriever = get_dense_retriever()
    print (dense_retriever)

   


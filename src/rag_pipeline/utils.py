import re
import json
from typing import List, Dict, Optional, Tuple
from textwrap import shorten

def extract_document_metadata(content: str) -> Dict:
    """Extract document-level metadata from the markdown content."""
    metadata = {}

    # Extract document metadata block
    doc_meta_match = re.search(r'<document_metadata>\s*(\{.*?\})\s*</document_metadata>', content, re.DOTALL)
    if doc_meta_match:
        try:
            metadata = json.loads(doc_meta_match.group(1))
        except json.JSONDecodeError:
            pass

    return metadata

def extract_page_metadata(page_content: str) -> Dict:
    """Extract page-level metadata from page content."""
    metadata = {}

    # Extract page metadata block
    page_meta_match = re.search(r'<page_metadata>\s*(\{.*?\})\s*</page_metadata>', page_content, re.DOTALL)
    if page_meta_match:
        try:
            metadata = json.loads(page_meta_match.group(1))
        except json.JSONDecodeError:
            pass

    return metadata

def extract_page_header(header_content: str): # type: ignore
    return [line.strip() for line in header_content.splitlines() if line.strip()]

def parse_markdown(path: str) -> List[Dict]:
    with open(path, 'r', encoding='utf-8') as f:
        content = f.read()

    doc_metadata = extract_document_metadata(content)

    sections = []
    pages = re.split(r'---', content)[1:]  # Skip first empty part
    for page_content in pages:
        page_metadata = extract_page_metadata(page_content)

        match = re.search(r"<page_start>(\d+)</page_start>", page_content)
        if match:
            page_num = int(match.group(1))

        header_match = re.search(r"<header>(.*?)</header>", page_content, re.S)
        headers = []
        if header_match:
            header_content = header_match.group(1)
            headers = extract_page_header(header_content)

        clean_content = re.sub(r'<page_metadata>.*?</page_metadata>', '', page_content, flags=re.DOTALL)
        clean_content = re.sub(r'<page_start>\d+</page_start>', '', clean_content)
        clean_content = re.sub(r'<page_end>\d+</page_end>', '', clean_content)
        clean_content = re.sub(r'<header>.*?</header>', '', clean_content, flags=re.DOTALL)        

        clean_content = clean_content.strip()

        data = {
            "page_number": page_num,
            "page_metadata": page_metadata,
            "heading": headers,
            "content": clean_content,
        }
        sections.append(data)
    return (doc_metadata,sections)

def log_retrieval_results(results: List[Dict], k: int) -> None:
    if not results:
        print("Retrieval returned 0 documents.")
        return
    print("Top %d results:", min(k, len(results)))
    header = f"{'chunk_id':<14} {'score':<7} preview"
    print(header)
    print("-" * len(header))
    for row in results[:k]:
        preview = shorten(row.get("text", ""), width=60, placeholder="…")
        print("%s %-7.3f %s", str(row.get("chunk_id"))[:12], row.get("score", 0.0), preview) 
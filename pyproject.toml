[tool.poetry]
name = "vanilla-rag"
version = "0.1.0"
description = "Vanilla RAG prototype for legal doc QA"
authors = ["osman"]

packages = [
    { include = "rag_pipeline", from = "src" }
]

[tool.poetry.dependencies]
python = "^3.12"
sentence-transformers = "^2.2.2"
faiss-cpu = "^1.7.4"
rank_bm25 = "^0.2.2"
transformers = "^4.40.0"
torch = "^2.0.0"
accelerate = "^0.20.3"
json5 = "^0.9.9"
lancedb = "^0.25.0"
httpx = "^0.28.1"
pandas = "^2.3.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

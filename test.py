#!/usr/bin/env python3

import rag_pipeline.ingest as ingest
import rag_pipeline.query as query

import glob
import os

if __name__ == "__main__":

    # #INDEX
    # resources_dir_name = "resources"
    # project_root = os.path.dirname(__file__)
    # resources_path = os.path.join(project_root, resources_dir_name)
    # md_files = glob.glob(os.path.join(resources_path, "*.md"))    
    # #print(md_files)

    # ingest.ingest(md_files)

    questions = ["What is article one about?"]
    answers = query(questions)
    print (answers)